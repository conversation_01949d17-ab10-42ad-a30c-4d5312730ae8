<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;

use Modules\RajaCms\Filament\Resources\CmsResource;
use Modules\RajaJson\Filament\Resources\Pages\AutoRajajsonEditRecord;
use Filament\Actions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\HasMedia;
 
 

class EditCms extends AutoRajajsonEditRecord
{
    protected static string $resource = CmsResource::class;
 
 
 
 

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load rajajson data dulu
        $data = parent::mutateFormDataBeforeFill($data);

        // Kemudian tambahkan data JSON (untuk jcol fields)
        if ($this->record) {
            $jcolData = $this->record->getJcolFormData();
            $data = array_merge($data, $jcolData);
        }

        return $data;
    }

    /**
     * Process RajaGambarUpload files after save
     */
    protected function afterSave(): void
    {
        // Call parent method first
        parent::afterSave();

        // Process RajaGambarUpload files
        $this->processRajaGambarUploads();
    }

    /**
     * Process uploaded files from RajaGambarUpload component
     */
    // protected function processRajaGambarUploads(): void
    // {
    //     Log::info('Processing RajaGambarUpload files for record: ' . $this->record->id);

    //     // Get form data
    //     $formData = $this->form->getState();
    //     Log::info('Form data keys: ' . json_encode(array_keys($formData)));

    //     // Check if record implements HasMedia
    //     if (!$this->record instanceof HasMedia) {
    //         Log::warning('Record does not implement HasMedia interface');
    //         return;
    //     }

    //     // Process gambar field (from RajaGambarUpload)
    //     Log::info('Checking gambar field. Value: ' . json_encode($formData['gambar'] ?? 'NOT_SET'));
    //     Log::info('Gambar field isset: ' . (isset($formData['gambar']) ? 'true' : 'false'));
    //     Log::info('Gambar field empty: ' . (empty($formData['gambar']) ? 'true' : 'false'));

    //     if (isset($formData['gambar'])) {
    //         Log::info('Found gambar field with data: ' . json_encode($formData['gambar']));

    //         if (empty($formData['gambar'])) {
    //             Log::info('Gambar field is empty');
    //             return;
    //         }

    //         foreach ((array) $formData['gambar'] as $fileUuid => $fileData) {
    //             Log::info("Processing file UUID: $fileUuid with data: " . json_encode($fileData));

    //             // PERBAIKAN: Handle different data formats
    //             $tempPath = null;

    //             if (is_string($fileData)) {
    //                 // Jika fileData adalah string path (seperti "uploads/rajagambar/noimage.jpg")
    //                 if (strpos($fileData, 'uploads/') === 0) {
    //                     // File sudah dipindah ke final location - cek di storage/ langsung
    //                     $tempPath = storage_path($fileData);
    //                     Log::info("Using final file path: $tempPath");
    //                 } else {
    //                     // Coba sebagai UUID di livewire-tmp
    //                     $tempPath = storage_path("app/livewire-tmp/$fileData");
    //                     Log::info("Using UUID as filename: $tempPath");
    //                 }
    //             } else {
    //                 // Original logic untuk UUID
    //                 $tempPath = storage_path("app/livewire-tmp/$fileUuid");
    //                 Log::info("Using UUID from key: $tempPath");
    //             }

    //             Log::info("Looking for file at: $tempPath");

    //             if (!file_exists($tempPath)) {
    //                 Log::warning("File not found: $tempPath");

    //                 // Try alternative paths
    //                 $altPaths = [
    //                     storage_path("app/livewire-tmp/$fileUuid"),
    //                     storage_path("app/public/livewire-tmp/$fileUuid"),
    //                     storage_path("app/public/$fileData"), // Jika fileData adalah path
    //                     storage_path($fileData) // Coba langsung di storage/
    //                 ];

    //                 Log::info("Trying alternative paths: " . implode(', ', $altPaths));

    //                 $found = false;
    //                 foreach ($altPaths as $altPath) {
    //                     if (file_exists($altPath)) {
    //                         $tempPath = $altPath;
    //                         Log::info("Found file at alternative path: $altPath");
    //                         $found = true;
    //                         break;
    //                     }
    //                 }

    //                 if (!$found) {
    //                     Log::warning("File not found in any location");
    //                     continue;
    //                 }
    //             }

    //             try {
    //                 Log::info("Attempting to add file to media collection: $tempPath");

    //                 // Add file to media collection
    //                 $media = $this->record->addMedia($tempPath)
    //                     ->usingName(basename($tempPath))
    //                     ->toMediaCollection('featured');

    //                 Log::info('File successfully added to media collection: ' . $media->id);

    //                 // Generate thumbnails (basic implementation)
    //                 $this->generateThumbnails($media);

    //             } catch (\Exception $e) {
    //                 Log::error("Failed to process file $fileUuid: " . $e->getMessage());
    //                 Log::error("Exception trace: " . $e->getTraceAsString());
    //             }
    //         }
    //     } else {
    //         Log::info('No gambar field found in form data');
    //     }
    // }

    /**
     * Generate thumbnails for uploaded media
     */
    // protected function generateThumbnails($media): void
    // {
    //     try {
    //         // Basic thumbnail generation - can be enhanced later
    //         Log::info('Generating thumbnails for media: ' . $media->id);

    //         // This is a placeholder - actual thumbnail generation would be implemented here
    //         // For now, just log that we would generate thumbnails

    //     } catch (\Exception $e) {
    //         Log::error("Failed to generate thumbnails for media {$media->id}: " . $e->getMessage());
    //     }
    // }

    /**
     * Tambahkan tombol save di header (di atas form)
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('simpanPerubahan')
                ->label('Simpan Perubahan')
                ->color('success')
                ->icon('heroicon-o-check-circle')
                ->action(function () {
                    $this->save();
                })
                ->extraAttributes([
                    'wire:loading.attr' => 'disabled',
                    'wire:loading.class' => 'opacity-70 cursor-wait',
                    'class' => 'filament-button-tunggu'
                ])
                ->iconPosition('after'),
        ];
    }

    /**
     * Kustomisasi tombol form actions (di bawah form)
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    /**
     * Kustomisasi tombol save dengan indikator loading
     */
    protected function getSaveFormAction(): Actions\Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan Perubahan')
            ->color('success')
            ->icon('heroicon-o-check-circle')
            ->extraAttributes([
                'wire:loading.attr' => 'disabled',
                'wire:loading.class' => 'opacity-70 cursor-wait',
                'class' => 'filament-button-tunggu'
            ])
            ->iconPosition('after');
    }

    // public function getRedirectUrl(): string
    // {
        // return CmsResource::getUrl('index');
    // }
}
