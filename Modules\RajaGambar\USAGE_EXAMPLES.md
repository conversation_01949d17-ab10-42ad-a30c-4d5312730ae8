# RajaGambar Module - Usage Examples

## Installation

The RajaGambar module comes with intervention/image v3 pre-installed and configured.

## Configuration

All configuration options are available in `config/config.php`. You can also use environment variables:

```env
# Image Processing
RAJAGAMBAR_IMAGE_DRIVER=gd
RAJAGAMBAR_IMAGE_QUALITY=85
RAJAGAMBAR_MAX_WIDTH=2048
RAJAGAMBAR_MAX_HEIGHT=2048

# WebP Settings
RAJAGAMBAR_WEBP_ENABLED=true
RAJAGAMBAR_WEBP_QUALITY=80

# Thumbnails
RAJAGAMBAR_THUMBNAILS_ENABLED=true
RAJAGAMBAR_THUMBNAILS_SUFFIX=_th

# Storage
RAJAGAMBAR_STORAGE_PATH=uploads
RAJAGAMBAR_STORAGE_DISK=public
```

## Basic Usage

### Using the Service Directly

```php
use Modules\RajaGambar\Services\InterventionImageService;

// Get service instance
$imageService = app(InterventionImageService::class);
// or
$imageService = resolve('rajagambar.image');

// Process an image
$imageService->processImage('/path/to/input.jpg', '/path/to/output.jpg', [
    'width' => 800,
    'height' => 600,
    'quality' => 90
]);

// Create thumbnail
$imageService->createThumbnail('/path/to/input.jpg', '/path/to/thumb.jpg', 300, 300);

// Get image information
$info = $imageService->getImageInfo('/path/to/image.jpg');
// Returns: ['width' => 1920, 'height' => 1080, 'mime_type' => 'image/jpeg', ...]

// Validate image format
$isValid = $imageService->isValidImageFormat('/path/to/image.jpg');
```

### Using Helper Functions

```php
// Process image
rajagambar_process('/path/to/input.jpg', '/path/to/output.jpg', [
    'width' => 800,
    'height' => 600,
    'quality' => 90
]);

// Create thumbnail
rajagambar_thumbnail('/path/to/input.jpg', '/path/to/thumb.jpg', 300, 300);

// Get image info
$info = rajagambar_info('/path/to/image.jpg');

// Validate image
$isValid = rajagambar_validate('/path/to/image.jpg');

// Get service instance
$service = rajagambar_service();
```

## Advanced Usage

### Custom Image Processing

```php
$imageService = rajagambar_service();

// Load image
$image = $imageService->loadImage('/path/to/image.jpg');

// Apply custom transformations
$image = $imageService->resize($image, 800, 600);
$image = $imageService->crop($image, 400, 400, 100, 100);

// Save processed image
$image->save('/path/to/processed.jpg');
```

### Batch Processing

```php
$imageService = rajagambar_service();
$images = ['/path/to/img1.jpg', '/path/to/img2.jpg', '/path/to/img3.jpg'];

foreach ($images as $imagePath) {
    $outputPath = str_replace('.jpg', '_processed.jpg', $imagePath);
    
    $imageService->processImage($imagePath, $outputPath, [
        'max_width' => 1200,
        'max_height' => 1200,
        'quality' => 85
    ]);
}
```

### Creating Multiple Thumbnails

```php
$imageService = rajagambar_service();
$inputPath = '/path/to/original.jpg';

$thumbnailSizes = [
    'small' => [150, 150],
    'medium' => [300, 300],
    'large' => [600, 600]
];

foreach ($thumbnailSizes as $size => [$width, $height]) {
    $outputPath = "/path/to/thumbnails/{$size}_thumbnail.jpg";
    $imageService->createThumbnail($inputPath, $outputPath, $width, $height);
}
```

### WebP Conversion

```php
$imageService = rajagambar_service();

// Load and convert to WebP
$image = $imageService->loadImage('/path/to/image.jpg');
$webpImage = $imageService->convertToWebp($image, 80); // 80% quality
$webpImage->save('/path/to/image.webp');
```

## Configuration Options

### Image Processing Options

```php
$options = [
    'width' => 800,              // Target width
    'height' => 600,             // Target height
    'quality' => 90,             // Image quality (1-100)
    'aspect_ratio' => true,      // Maintain aspect ratio
    'max_width' => 1200,         // Maximum width constraint
    'max_height' => 1200,        // Maximum height constraint
    'crop' => [                  // Crop settings
        'width' => 400,
        'height' => 400,
        'x' => 100,              // X position (optional)
        'y' => 100               // Y position (optional)
    ]
];

rajagambar_process('/input.jpg', '/output.jpg', $options);
```

### Thumbnail Options

```php
$thumbnailOptions = [
    'quality' => 80,             // Thumbnail quality
    'fit' => 'cover'             // 'cover' or 'contain'
];

rajagambar_thumbnail('/input.jpg', '/thumb.jpg', 300, 300, $thumbnailOptions);
```

## Error Handling

```php
try {
    $imageService = rajagambar_service();
    $imageService->processImage('/input.jpg', '/output.jpg');
} catch (Exception $e) {
    // Handle processing errors
    Log::error('Image processing failed: ' . $e->getMessage());
}
```

## Integration with FilamentPHP

The service can be easily integrated with FilamentPHP components:

```php
use Modules\RajaGambar\Services\InterventionImageService;

class MyFilamentResource extends Resource
{
    public function afterSave(): void
    {
        $imageService = app(InterventionImageService::class);
        
        // Process uploaded images
        if ($this->record->image_path) {
            $imageService->processImage(
                storage_path('app/public/' . $this->record->image_path),
                storage_path('app/public/processed/' . $this->record->image_path)
            );
        }
    }
}
```
