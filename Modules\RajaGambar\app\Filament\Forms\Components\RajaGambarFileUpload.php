<?php

namespace Modules\RajaGambar\Filament\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Log;
use Modules\RajaGambar\Models\RajaGambar;
use Modules\RajaGambar\Services\InterventionImageService;

class RajaGambarFileUpload extends FileUpload
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Hook into the afterStateUpdated event
        $this->afterStateUpdated(function ($state, $component) {
            if (empty($state)) {
                return;
            }

            // Process each uploaded file
            foreach ((array) $state as $filePath) {
                $this->recordFileInRajaGambar($filePath);
            }
        });
    }

    /**
     * Record uploaded file in raja_gambar table
     */
    protected function recordFileInRajaGambar(string $filePath): void
    {
        try {
            $imageService = app(InterventionImageService::class);
            
            // Get full path
            $fullPath = storage_path('app/public/' . $filePath);
            
            // Only process if file exists and is valid image
            if (!file_exists($fullPath) || !$imageService->isValidImageFormat($fullPath)) {
                return;
            }

            // Check if already recorded
            if (RajaGambar::where('file', $filePath)->exists()) {
                return;
            }

            // Get image information
            $imageInfo = $imageService->getImageInfo($fullPath);
            
            // Create record
            RajaGambar::create([
                'file' => $filePath,
                'json' => [
                    'original_name' => basename($filePath),
                    'file_size' => $imageInfo['file_size'] ?? 0,
                    'width' => $imageInfo['width'] ?? 0,
                    'height' => $imageInfo['height'] ?? 0,
                    'mime_type' => $imageInfo['mime_type'] ?? '',
                    'uploaded_at' => now()->toISOString(),
                    'source' => 'raja_gambar_file_upload',
                    'user_id' => auth()->id(),
                    'component' => static::class,
                ]
            ]);

            Log::info("File recorded in raja_gambar via RajaGambarFileUpload: {$filePath}");

        } catch (\Exception $e) {
            Log::error("Failed to record file in raja_gambar: " . $e->getMessage());
        }
    }

    /**
     * Create a new RajaGambarFileUpload instance
     */
    public static function make(string $name): static
    {
        $static = app(static::class, ['name' => $name]);
        $static->configure();

        return $static;
    }
}
