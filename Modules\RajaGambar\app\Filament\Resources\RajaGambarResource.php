<?php

namespace Modules\RajaGambar\Filament\Resources;

use Modules\RajaGambar\Filament\Resources\RajaGambarResource\Pages;
use Modules\RajaGambar\Filament\Resources\RajaGambarResource\RelationManagers;
use Modules\RajaGambar\Models\RajaGambar;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RajaGambarResource extends Resource
{
    protected static ?string $model = RajaGambar::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('file')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\Textarea::make('json')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('file')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRajaGambars::route('/'),
            'create' => Pages\CreateRajaGambar::route('/create'),
            'edit' => Pages\EditRajaGambar::route('/{record}/edit'),
        ];
    }
}
