<?php

namespace Modules\RajaGambar\Filament\Resources;

use Modules\RajaGambar\Filament\Resources\RajaGambarResource\Pages;
use Modules\RajaGambar\Filament\Resources\RajaGambarResource\RelationManagers;
use Modules\RajaGambar\Models\RajaGambar;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

use Illuminate\Support\Str;

class RajaGambarResource extends Resource
{
    protected static ?string $model = RajaGambar::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Raja Gambar';

    protected static ?string $modelLabel = 'Gambar';

    protected static ?string $pluralModelLabel = 'Gambar';

    public static function getExtraBodyAttributes(): array
    {
        return [
            'class' => 'raja-gambar-gallery-page'
        ];
    }

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'file';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi File')
                    ->schema([
                        Forms\Components\TextInput::make('file')
                            ->label('Path File')
                            ->maxLength(200)
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('model_type')
                            ->label('Model Type')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('model_id')
                            ->label('Model ID')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('field_name')
                            ->label('Field Name')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('user_id')
                            ->label('User ID')
                            ->disabled()
                            ->dehydrated(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Preview Gambar')
                    ->schema([
                        Forms\Components\ViewField::make('image_preview')
                            ->label('')
                            ->view('rajagambar::filament.image-preview')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Metadata JSON')
                    ->schema([
                        Forms\Components\Textarea::make('json')
                            ->label('JSON Data')
                            ->rows(10)
                            ->disabled()
                            ->dehydrated(false)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    // Image Preview
                    Tables\Columns\ImageColumn::make('file')
                        ->label('')
                        ->disk('public')
                        ->getStateUsing(function ($record) {
                            // Return just the file path, let FilamentPHP handle the URL generation
                            return $record->file;
                        })
                        ->defaultImageUrl('/noimage.jpg')
                        ->height(200)
                        ->width('100%')
                        ->extraAttributes([
                            'class' => 'rounded-lg object-cover cursor-pointer hover:opacity-80 transition-opacity shadow-sm border border-gray-200',
                            'style' => 'aspect-ratio: 1/1;'
                        ])
                        ->action(
                            Tables\Actions\ViewAction::make()
                                ->modalWidth('4xl')
                        ),

                    // File Name
                    Tables\Columns\TextColumn::make('file')
                        ->label('')
                        ->formatStateUsing(fn ($record) => basename($record->file))
                        ->weight('semibold')
                        ->size('sm')
                        ->color('gray')
                        ->limit(20)
                        ->tooltip(fn ($record) => $record->file)
                        ->extraAttributes(['class' => 'text-center mt-2']),

                    // Dimensions & Size Info
                    Tables\Columns\TextColumn::make('info_display')
                        ->label('')
                        ->formatStateUsing(function ($record) {
                            $json = is_array($record->json) ? $record->json : json_decode($record->json, true);
                            if (!$json) return '-';

                            $info = [];
                            if (isset($json['width']) && isset($json['height'])) {
                                $info[] = $json['width'] . 'x' . $json['height'];
                            }
                            if (isset($json['file_size'])) {
                                $info[] = number_format($json['file_size'] / 1024, 1) . ' KB';
                            }

                            return implode(' • ', $info);
                        })
                        ->size('xs')
                        ->color('gray')
                        ->extraAttributes(['class' => 'text-center']),

                    // Model Info
                    Tables\Columns\TextColumn::make('model_info')
                        ->label('')
                        ->formatStateUsing(function ($record) {
                            $modelName = $record->model_type ? class_basename($record->model_type) : 'Unknown';
                            return "{$modelName} #{$record->model_id}";
                        })
                        ->size('xs')
                        ->color('primary')
                        ->badge()
                        ->extraAttributes(['class' => 'text-center']),

                    // Created Date
                    Tables\Columns\TextColumn::make('created_at')
                        ->label('')
                        ->dateTime('M j, Y')
                        ->size('xs')
                        ->color('gray')
                        ->extraAttributes(['class' => 'text-center']),
                ])
                ->space(2)
                ->alignment('center'),
            ])
            ->filters([
                SelectFilter::make('model_type')
                    ->label('Model Type')
                    ->options(function () {
                        return RajaGambar::distinct()
                            ->pluck('model_type', 'model_type')
                            ->mapWithKeys(fn ($value, $key) => [$key => class_basename($value)])
                            ->toArray();
                    }),

                SelectFilter::make('field_name')
                    ->label('Field Name')
                    ->options(function () {
                        return RajaGambar::distinct()
                            ->pluck('field_name', 'field_name')
                            ->filter()
                            ->toArray();
                    }),

                Filter::make('has_user')
                    ->label('Has User')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('user_id')),

                Filter::make('system_uploads')
                    ->label('System Uploads')
                    ->query(fn (Builder $query): Builder => $query->whereNull('user_id')),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->modalWidth('4xl'),
                    Tables\Actions\EditAction::make()
                        ->modalWidth('4xl'),
                    Tables\Actions\DeleteAction::make()
                        ->requiresConfirmation(),
                ])
                ->icon('heroicon-m-ellipsis-vertical')
                ->size('sm')
                ->color('gray')
                ->button(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([15, 30, 60, 120])
            ->contentGrid([
                'sm' => 2,
                'md' => 3,
                'lg' => 4,
                'xl' => 5,
                '2xl' => 6,
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Preview Gambar')
                    ->schema([
                        Infolists\Components\ViewEntry::make('image_preview')
                            ->label('')
                            ->view('rajagambar::filament.image-preview-info')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Informasi File')
                    ->schema([
                        Infolists\Components\TextEntry::make('file')
                            ->label('Path File')
                            ->copyable(),

                        Infolists\Components\TextEntry::make('model_type')
                            ->label('Model Type')
                            ->formatStateUsing(fn ($state) => $state ? class_basename($state) : '-'),

                        Infolists\Components\TextEntry::make('model_id')
                            ->label('Model ID'),

                        Infolists\Components\TextEntry::make('field_name')
                            ->label('Field Name')
                            ->badge(),

                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Uploaded by')
                            ->default('System'),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Metadata Detail')
                    ->schema([
                        Infolists\Components\TextEntry::make('json')
                            ->label('File Information')
                            ->formatStateUsing(function ($record) {
                                $json = is_array($record->json) ? $record->json : json_decode($record->json, true);
                                if (!$json) return 'No metadata available';

                                $formatted = [];
                                foreach ($json as $key => $value) {
                                    $label = ucwords(str_replace('_', ' ', $key));
                                    if ($key === 'file_size' && is_numeric($value)) {
                                        $formatted[] = $label . ': ' . number_format($value / 1024, 2) . ' KB';
                                    } elseif ($key === 'uploaded_at') {
                                        $formatted[] = $label . ': ' . date('Y-m-d H:i:s', strtotime($value));
                                    } elseif (in_array($key, ['width', 'height']) && is_numeric($value)) {
                                        $formatted[] = $label . ': ' . $value . ' px';
                                    } else {
                                        $formatted[] = $label . ': ' . (is_string($value) ? $value : json_encode($value));
                                    }
                                }
                                return implode('<br>', $formatted);
                            })
                            ->html(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRajaGambars::route('/'),
            'create' => Pages\CreateRajaGambar::route('/create'),
            'edit' => Pages\EditRajaGambar::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return false; // Prevent manual creation since files are auto-recorded
    }
}
