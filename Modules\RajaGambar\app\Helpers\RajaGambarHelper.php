<?php

use <PERSON><PERSON><PERSON>\RajaGambar\Services\InterventionImageService;

if (!function_exists('rajagambar_service')) {
    /**
     * Get RajaGambar InterventionImageService instance
     */
    function rajagambar_service(): InterventionImageService
    {
        return app(InterventionImageService::class);
    }
}

if (!function_exists('rajagambar_process')) {
    /**
     * Process image using RajaGambar service
     */
    function rajagambar_process(string $inputPath, string $outputPath, array $options = []): bool
    {
        return rajagambar_service()->processImage($inputPath, $outputPath, $options);
    }
}

if (!function_exists('rajagambar_thumbnail')) {
    /**
     * Create thumbnail using RajaGambar service
     */
    function rajagambar_thumbnail(string $inputPath, string $outputPath, int $width, int $height, array $options = []): bool
    {
        return rajagambar_service()->createThumbnail($inputPath, $outputPath, $width, $height, $options);
    }
}

if (!function_exists('rajagambar_info')) {
    /**
     * Get image information using RajaGambar service
     */
    function rajagambar_info(string $path): array
    {
        return rajagambar_service()->getImageInfo($path);
    }
}

if (!function_exists('rajagambar_validate')) {
    /**
     * Validate image format using RajaGambar service
     */
    function rajagambar_validate(string $path): bool
    {
        return rajagambar_service()->isValidImageFormat($path);
    }
}
