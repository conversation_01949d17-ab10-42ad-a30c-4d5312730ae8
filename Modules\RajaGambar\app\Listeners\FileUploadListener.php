<?php

namespace Modules\RajaGambar\Listeners;

use Illuminate\Support\Facades\Log;
use Livewire\Features\SupportFileUploads\FileUploadConfiguration;
use Modules\RajaGambar\Models\RajaGambar;
use Modules\RajaGambar\Services\InterventionImageService;

class FileUploadListener
{
    protected InterventionImageService $imageService;

    public function __construct(InterventionImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Handle file upload completion
     */
    public function handle($event): void
    {
        // Check if this is a file upload event
        if (!isset($event->file) || !isset($event->path)) {
            return;
        }

        $filePath = $event->path;
        $originalName = $event->file->getClientOriginalName() ?? basename($filePath);

        // Only process image files
        if (!$this->imageService->isValidImageFormat($filePath)) {
            return;
        }

        try {
            // Get image information
            $imageInfo = $this->imageService->getImageInfo($filePath);
            
            // Create record in raja_gambar table
            RajaGambar::create([
                'file' => $filePath,
                'json' => [
                    'original_name' => $originalName,
                    'file_size' => $imageInfo['file_size'] ?? 0,
                    'width' => $imageInfo['width'] ?? 0,
                    'height' => $imageInfo['height'] ?? 0,
                    'mime_type' => $imageInfo['mime_type'] ?? '',
                    'uploaded_at' => now()->toISOString(),
                    'source' => 'filament_upload',
                    'user_id' => auth()->id(),
                ]
            ]);

            Log::info("File recorded in raja_gambar: {$filePath}");

        } catch (\Exception $e) {
            Log::error("Failed to record file in raja_gambar: " . $e->getMessage());
        }
    }
}
