<?php

namespace Modules\RajaGambar\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver as GdDriver;
use Intervention\Image\Drivers\Imagick\Driver as ImagickDriver;
use Intervention\Image\Interfaces\ImageInterface;
use Intervention\Image\Geometry\Factories\RectangleFactory;
use Exception;

class InterventionImageService
{
    protected ImageManager $manager;
    protected array $config;

    public function __construct()
    {
        $this->config = Config::get('rajagambar', []);
        $this->initializeImageManager();
    }

    /**
     * Initialize Image Manager with configured driver
     */
    protected function initializeImageManager(): void
    {
        $driver = $this->getImageDriver();
        
        $this->manager = new ImageManager(
            $driver === 'imagick' ? new ImagickDriver() : new GdDriver()
        );
    }

    /**
     * Get image driver from config
     */
    public function getImageDriver(): string
    {
        return $this->config['image']['driver'] ?? 'gd';
    }

    /**
     * Get image quality from config
     */
    public function getImageQuality(): int
    {
        return $this->config['image']['quality'] ?? 85;
    }

    /**
     * Check if auto orientation is enabled
     */
    public function isAutoOrientEnabled(): bool
    {
        return $this->config['image']['auto_orient'] ?? true;
    }

    /**
     * Check if metadata should be stripped
     */
    public function shouldStripMetadata(): bool
    {
        return $this->config['image']['strip_metadata'] ?? true;
    }

    /**
     * Get maximum image width
     */
    public function getMaxWidth(): ?int
    {
        return $this->config['image']['max_width'] ?? null;
    }

    /**
     * Get maximum image height
     */
    public function getMaxHeight(): ?int
    {
        return $this->config['image']['max_height'] ?? null;
    }

    /**
     * Get supported image formats
     */
    public function getSupportedFormats(): array
    {
        return $this->config['image']['supported_formats'] ?? ['jpeg', 'jpg', 'png', 'gif', 'webp'];
    }

    /**
     * Check if WebP conversion is enabled
     */
    public function isWebpEnabled(): bool
    {
        return $this->config['image']['webp']['enabled'] ?? true;
    }

    /**
     * Get WebP quality
     */
    public function getWebpQuality(): int
    {
        return $this->config['image']['webp']['quality'] ?? 80;
    }

    /**
     * Load image from file path
     */
    public function loadImage(string $path): ImageInterface
    {
        return $this->manager->read($path);
    }

    /**
     * Load image from binary data
     */
    public function loadFromBinary(string $data): ImageInterface
    {
        return $this->manager->read($data);
    }

    /**
     * Resize image with constraints
     */
    public function resize(ImageInterface $image, ?int $width = null, ?int $height = null, bool $aspectRatio = true): ImageInterface
    {
        if ($width || $height) {
            if ($aspectRatio) {
                $image = $image->scale($width, $height);
            } else {
                $image = $image->resize($width, $height);
            }
        }

        return $image;
    }

    /**
     * Crop image to specific dimensions
     */
    public function crop(ImageInterface $image, int $width, int $height, ?int $x = null, ?int $y = null): ImageInterface
    {
        if ($x !== null && $y !== null) {
            return $image->crop($width, $height, $x, $y);
        }

        return $image->cover($width, $height);
    }

    /**
     * Apply quality settings to image
     */
    public function applyQuality(ImageInterface $image, ?int $quality = null): ImageInterface
    {
        $quality = $quality ?? $this->getImageQuality();
        
        return $image->encodeByMediaType(quality: $quality);
    }

    /**
     * Convert image to WebP format
     */
    public function convertToWebp(ImageInterface $image, ?int $quality = null): ImageInterface
    {
        $quality = $quality ?? $this->getWebpQuality();
        
        return $image->toWebp($quality);
    }

    /**
     * Process image with all configured settings
     */
    public function processImage(string $inputPath, string $outputPath, array $options = []): bool
    {
        try {
            $image = $this->loadImage($inputPath);

            // Apply auto orientation if enabled
            if ($this->isAutoOrientEnabled()) {
                $image = $image->orient();
            }

            // Apply resize constraints if configured
            $maxWidth = $options['max_width'] ?? $this->getMaxWidth();
            $maxHeight = $options['max_height'] ?? $this->getMaxHeight();
            
            if ($maxWidth || $maxHeight) {
                $image = $this->resize($image, $maxWidth, $maxHeight);
            }

            // Apply custom resize if specified
            if (isset($options['width']) || isset($options['height'])) {
                $image = $this->resize(
                    $image, 
                    $options['width'] ?? null, 
                    $options['height'] ?? null,
                    $options['aspect_ratio'] ?? true
                );
            }

            // Apply crop if specified
            if (isset($options['crop'])) {
                $crop = $options['crop'];
                $image = $this->crop(
                    $image,
                    $crop['width'],
                    $crop['height'],
                    $crop['x'] ?? null,
                    $crop['y'] ?? null
                );
            }

            // Apply quality
            $quality = $options['quality'] ?? $this->getImageQuality();
            $image = $this->applyQuality($image, $quality);

            // Save processed image
            $image->save($outputPath);

            return true;
        } catch (Exception $e) {
            throw new Exception("Failed to process image: " . $e->getMessage());
        }
    }

    /**
     * Create thumbnail from image
     */
    public function createThumbnail(string $inputPath, string $outputPath, int $width, int $height, array $options = []): bool
    {
        try {
            $image = $this->loadImage($inputPath);

            // Apply auto orientation if enabled
            if ($this->isAutoOrientEnabled()) {
                $image = $image->orient();
            }

            // Create thumbnail using cover (crop to fit)
            $fit = $options['fit'] ?? 'cover';
            
            if ($fit === 'cover') {
                $image = $image->cover($width, $height);
            } else {
                $image = $image->scale($width, $height);
            }

            // Apply quality
            $quality = $options['quality'] ?? $this->getImageQuality();
            $image = $this->applyQuality($image, $quality);

            // Save thumbnail
            $image->save($outputPath);

            return true;
        } catch (Exception $e) {
            throw new Exception("Failed to create thumbnail: " . $e->getMessage());
        }
    }

    /**
     * Get image information
     */
    public function getImageInfo(string $path): array
    {
        try {
            $image = $this->loadImage($path);
            
            return [
                'width' => $image->width(),
                'height' => $image->height(),
                'mime_type' => $image->origin()->mediaType(),
                'file_size' => filesize($path),
                'format' => pathinfo($path, PATHINFO_EXTENSION),
            ];
        } catch (Exception $e) {
            throw new Exception("Failed to get image info: " . $e->getMessage());
        }
    }

    /**
     * Validate if file is a supported image format
     */
    public function isValidImageFormat(string $path): bool
    {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        return in_array($extension, $this->getSupportedFormats());
    }

    /**
     * Get storage path for uploads
     */
    public function getStoragePath(): string
    {
        return $this->config['storage']['path'] ?? 'uploads';
    }

    /**
     * Get storage disk
     */
    public function getStorageDisk(): string
    {
        return $this->config['storage']['disk'] ?? 'public';
    }
}
