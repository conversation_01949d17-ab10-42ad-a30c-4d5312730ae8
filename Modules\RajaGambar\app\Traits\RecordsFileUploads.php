<?php

namespace Modules\RajaGambar\Traits;

use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\RajaGambar\Models\RajaGambar;
use Modules\RajaGambar\Services\InterventionImageService;

trait RecordsFileUploads
{
    /**
     * Boot the trait
     */
    public static function bootRecordsFileUploads(): void
    {
        // Hook into model events
        static::saved(function ($model) {
            $model->processFileUploadsForRajaGambar();
        });
    }

    /**
     * Process file uploads and record them in raja_gambar
     */
    public function processFileUploadsForRajaGambar(): void
    {
        // Get all file upload fields from the model
        $fileFields = $this->getFileUploadFields();
        
        foreach ($fileFields as $field) {
            $value = $this->getAttribute($field);
            
            if (empty($value)) {
                continue;
            }

            // Handle both single file and array of files
            $files = is_array($value) ? $value : [$value];
            
            foreach ($files as $filePath) {
                if (is_string($filePath)) {
                    $this->recordFileInRajaGambar($filePath, $field);
                }
            }
        }
    }

    /**
     * Record a file in raja_gambar table
     */
    protected function recordFileInRajaGambar(string $filePath, string $fieldName): void
    {
        try {
            $imageService = app(InterventionImageService::class);
            
            // Get full path
            $fullPath = storage_path('app/public/' . $filePath);
            
            // Only process if file exists and is valid image
            if (!file_exists($fullPath) || !$imageService->isValidImageFormat($fullPath)) {
                return;
            }

            // Check if already recorded
            if (RajaGambar::where('file', $filePath)->exists()) {
                return;
            }

            // Get image information
            $imageInfo = $imageService->getImageInfo($fullPath);
            
            // Create record
            RajaGambar::create([
                'file' => $filePath,
                'json' => [
                    'original_name' => basename($filePath),
                    'file_size' => $imageInfo['file_size'] ?? 0,
                    'width' => $imageInfo['width'] ?? 0,
                    'height' => $imageInfo['height'] ?? 0,
                    'mime_type' => $imageInfo['mime_type'] ?? '',
                    'uploaded_at' => now()->toISOString(),
                    'source' => 'model_trait',
                    'user_id' => auth()->id(),
                    'model_type' => static::class,
                    'model_id' => $this->getKey(),
                    'field_name' => $fieldName,
                ]
            ]);

            Log::info("File recorded in raja_gambar via trait: {$filePath} from {$fieldName}");

        } catch (\Exception $e) {
            Log::error("Failed to record file in raja_gambar: " . $e->getMessage());
        }
    }

    /**
     * Get file upload fields from the model
     * Override this method in your model to specify which fields contain file uploads
     */
    protected function getFileUploadFields(): array
    {
        // Default file upload fields - override in your model
        return ['gambar', 'image', 'foto', 'file', 'attachment'];
    }
}
