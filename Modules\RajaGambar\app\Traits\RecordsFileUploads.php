<?php

namespace Modules\RajaGambar\Traits;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Modules\RajaGambar\Models\RajaGambar;
use Modules\RajaGambar\Services\InterventionImageService;

trait RecordsFileUploads
{
    /**
     * Original attributes before changes
     */
    protected $originalFileAttributes = [];

    /**
     * Boot the trait
     */
    public static function bootRecordsFileUploads(): void
    {
        // Store original attributes before saving
        static::saving(function ($model) {
            $model->storeOriginalFileAttributes();
        });

        // Process file uploads after saving
        static::saved(function ($model) {
            $model->processFileUploadsForRajaGambar();
        });

        // Clean up deleted files
        static::deleting(function ($model) {
            $model->cleanupFileRecords();
        });
    }

    /**
     * Store original file attributes before changes
     */
    protected function storeOriginalFileAttributes(): void
    {
        $fileFields = $this->getFileUploadFields();

        foreach ($fileFields as $field) {
            $this->originalFileAttributes[$field] = $this->getOriginal($field);
        }
    }

    /**
     * Process file uploads and record them in raja_gambar
     */
    public function processFileUploadsForRajaGambar(): void
    {
        $fileFields = $this->getFileUploadFields();

        Log::info("Processing file uploads for RajaGambar", [
            'model' => static::class,
            'model_id' => $this->getKey(),
            'file_fields' => $fileFields
        ]);

        foreach ($fileFields as $field) {
            $currentValue = $this->getAttribute($field);
            $originalValue = $this->originalFileAttributes[$field] ?? null;

            Log::info("Checking field: {$field}", [
                'current_value' => $currentValue,
                'original_value' => $originalValue
            ]);

            // Skip if no change
            if ($currentValue === $originalValue) {
                Log::info("No change in field: {$field}");
                continue;
            }

            // Process new files
            if (!empty($currentValue)) {
                $files = is_array($currentValue) ? $currentValue : [$currentValue];

                foreach ($files as $filePath) {
                    if (is_string($filePath) && !empty($filePath)) {
                        Log::info("Processing file: {$filePath} for field: {$field}");
                        $this->recordFileInRajaGambar($filePath, $field);
                    }
                }
            }
        }
    }

    /**
     * Clean up file records when model is deleted
     */
    protected function cleanupFileRecords(): void
    {
        $fileFields = $this->getFileUploadFields();

        foreach ($fileFields as $field) {
            $value = $this->getAttribute($field);

            if (empty($value)) {
                continue;
            }

            $files = is_array($value) ? $value : [$value];

            foreach ($files as $filePath) {
                if (is_string($filePath)) {
                    // Mark as deleted in raja_gambar
                    RajaGambar::where('file', $filePath)
                        ->where('model_type', static::class)
                        ->where('model_id', $this->getKey())
                        ->update([
                            'json->deleted_at' => now()->toISOString(),
                            'json->deleted_by' => $this->getCurrentUserId() ?? 'system',
                        ]);
                }
            }
        }
    }

    /**
     * Record a file in raja_gambar table
     */
    protected function recordFileInRajaGambar(string $filePath, string $fieldName): void
    {
        try {
            $imageService = app(InterventionImageService::class);

            // Get full path - handle different storage configurations
            $fullPath = $this->getFullFilePath($filePath);

            // Only process if file exists and is valid image
            if (!file_exists($fullPath) || !$imageService->isValidImageFormat($fullPath)) {
                Log::debug("Skipping file - not found or invalid image: {$fullPath}");
                return;
            }

            // Check if already recorded for this model
            $existingRecord = RajaGambar::where('file', $filePath)
                ->where('model_type', static::class)
                ->where('model_id', $this->getKey())
                ->where('field_name', $fieldName)
                ->first();

            if ($existingRecord) {
                Log::debug("File already recorded: {$filePath}");
                return;
            }

            // Get image information
            $imageInfo = $imageService->getImageInfo($fullPath);

            // Create record
            RajaGambar::create([
                'file' => $filePath,
                'json' => [
                    'original_name' => basename($filePath),
                    'file_size' => $imageInfo['file_size'] ?? filesize($fullPath),
                    'width' => $imageInfo['width'] ?? 0,
                    'height' => $imageInfo['height'] ?? 0,
                    'mime_type' => $imageInfo['mime_type'] ?? mime_content_type($fullPath),
                    'uploaded_at' => now()->toISOString(),
                    'source' => 'model_trait',
                    'user_id' => $this->getCurrentUserId(),
                    'model_type' => static::class,
                    'model_id' => $this->getKey(),
                    'field_name' => $fieldName,
                    'full_path' => $fullPath,
                ]
            ]);

            Log::info("File recorded in raja_gambar via trait: {$filePath} from {$fieldName} (Model: " . static::class . "#{$this->getKey()})");

        } catch (\Exception $e) {
            Log::error("Failed to record file in raja_gambar: " . $e->getMessage(), [
                'file_path' => $filePath,
                'field_name' => $fieldName,
                'model' => static::class,
                'model_id' => $this->getKey(),
            ]);
        }
    }

    /**
     * Get full file path based on storage configuration
     */
    protected function getFullFilePath(string $filePath): string
    {
        // Handle different path formats
        if (str_starts_with($filePath, '/')) {
            // Absolute path
            Log::debug("Using absolute path: {$filePath}");
            return $filePath;
        }

        // Try multiple possible locations
        $possiblePaths = [
            // Public storage path
            public_path('storage/' . $filePath),
            // Storage app public
            storage_path('app/public/' . $filePath),
            // Direct storage path
            storage_path($filePath),
            // Search in storage subdirectories (for Spatie Media Library)
            $this->findFileInStorageSubdirs($filePath),
        ];

        foreach ($possiblePaths as $path) {
            if ($path && file_exists($path)) {
                Log::debug("Found file at: {$path} for original path: {$filePath}");
                return $path;
            }
        }

        // Default fallback
        $defaultPath = storage_path('app/public/' . $filePath);
        Log::debug("File not found, using default path: {$defaultPath} for original: {$filePath}");
        return $defaultPath;
    }

    /**
     * Search for file in storage subdirectories (for Spatie Media Library)
     */
    protected function findFileInStorageSubdirs(string $filePath): ?string
    {
        $filename = basename($filePath);
        $storageBasePath = storage_path();

        // Search in numbered directories (Spatie Media Library pattern)
        for ($i = 1; $i <= 999; $i++) {
            $possiblePath = $storageBasePath . '/' . $i . '/' . $filename;
            if (file_exists($possiblePath)) {
                return $possiblePath;
            }
        }

        return null;
    }

    /**
     * Get current user ID safely
     */
    protected function getCurrentUserId(): ?int
    {
        try {
            return Auth::check() ? Auth::id() : null;
        } catch (\Exception) {
            return null;
        }
    }

    /**
     * Get file upload fields from the model
     * Override this method in your model to specify which fields contain file uploads
     */
    protected function getFileUploadFields(): array
    {
        // Check if model has a property defining file fields
        if (property_exists($this, 'fileUploadFields')) {
            return $this->fileUploadFields;
        }

        // Default file upload fields - override in your model
        return ['gambar', 'image', 'foto', 'file', 'attachment', 'banner', 'logo', 'thumbnail'];
    }

    /**
     * Get relationship to RajaGambar records
     */
    public function rajaGambarFiles()
    {
        return RajaGambar::where('model_type', static::class)
            ->where('model_id', $this->getKey())
            ->whereNull('json->deleted_at');
    }

    /**
     * Get all uploaded files for this model
     */
    public function getUploadedFiles(): \Illuminate\Support\Collection
    {
        return $this->rajaGambarFiles()->get();
    }

    /**
     * Get uploaded files by field name
     */
    public function getUploadedFilesByField(string $fieldName): \Illuminate\Support\Collection
    {
        return $this->rajaGambarFiles()
            ->where('json->field_name', $fieldName)
            ->get();
    }
}
