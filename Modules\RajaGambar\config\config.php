<?php

return [
    'name' => '<PERSON><PERSON><PERSON><PERSON>',

    /*
    |--------------------------------------------------------------------------
    | Storage Configuration
    |--------------------------------------------------------------------------
    */
    'storage' => [
        'disk' => env('RAJAGAMBAR_STORAGE_DISK', 'public'),
        'path' => env('RAJAGAMBAR_STORAGE_PATH', 'uploads'),
        'url_prefix' => env('RAJAGAMBAR_URL_PREFIX', '/storage'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Processing Configuration
    |--------------------------------------------------------------------------
    */
    'image' => [
        // Image driver: 'gd' or 'imagick'
        'driver' => env('RAJAGAMBAR_IMAGE_DRIVER', 'gd'),

        // Default image quality (1-100)
        'quality' => env('RAJAGAMBAR_IMAGE_QUALITY', 85),

        // Auto-orient images based on EXIF data
        'auto_orient' => env('RAJAGAMBAR_AUTO_ORIENT', true),

        // Strip metadata from images
        'strip_metadata' => env('RAJAGAMBAR_STRIP_METADATA', true),

        // Maximum image dimensions (null = no limit)
        'max_width' => env('RAJAGAMBAR_MAX_WIDTH', 2048),
        'max_height' => env('RAJAGAMBAR_MAX_HEIGHT', 2048),

        // Supported image formats
        'supported_formats' => ['jpeg', 'jpg', 'png', 'gif', 'webp', 'bmp', 'tiff'],

        // WebP conversion settings
        'webp' => [
            'enabled' => env('RAJAGAMBAR_WEBP_ENABLED', true),
            'quality' => env('RAJAGAMBAR_WEBP_QUALITY', 80),
            'preserve_original' => env('RAJAGAMBAR_WEBP_PRESERVE_ORIGINAL', true),
        ],

        // Progressive JPEG
        'progressive' => env('RAJAGAMBAR_PROGRESSIVE', true),

        // Background color for transparent images when converting
        'background_color' => env('RAJAGAMBAR_BACKGROUND_COLOR', 'ffffff'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Thumbnail Configuration
    |--------------------------------------------------------------------------
    */
    'thumbnails' => [
        'enabled' => env('RAJAGAMBAR_THUMBNAILS_ENABLED', true),
        'directory' => env('RAJAGAMBAR_THUMBNAILS_DIR', 'thumbnails'),
        'prefix' => env('RAJAGAMBAR_THUMBNAILS_PREFIX', ''),
        'suffix' => env('RAJAGAMBAR_THUMBNAILS_SUFFIX', '_th'),

        // Default thumbnail sizes
        'sizes' => [
            'small' => [
                'width' => 150,
                'height' => 150,
                'quality' => 80,
                'fit' => 'cover', // cover, contain, fill
                'enabled' => true,
            ],
            'medium' => [
                'width' => 300,
                'height' => 300,
                'quality' => 85,
                'fit' => 'cover',
                'enabled' => true,
            ],
            'large' => [
                'width' => 600,
                'height' => 600,
                'quality' => 90,
                'fit' => 'cover',
                'enabled' => true,
            ],
            // Percentage-based thumbnails
            '50p' => [
                'percentage' => 50,
                'quality' => 80,
                'enabled' => true,
            ],
            '25p' => [
                'percentage' => 25,
                'quality' => 75,
                'enabled' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Upload Configuration
    |--------------------------------------------------------------------------
    */
    'upload' => [
        'max_file_size' => env('RAJAGAMBAR_MAX_FILE_SIZE', 10240), // KB
        'max_files' => env('RAJAGAMBAR_MAX_FILES', 10),
        'chunk_size' => env('RAJAGAMBAR_CHUNK_SIZE', 1024), // KB
        'temporary_directory' => env('RAJAGAMBAR_TEMP_DIR', 'livewire-tmp'),

        // File validation
        'validate_dimensions' => env('RAJAGAMBAR_VALIDATE_DIMENSIONS', true),
        'min_width' => env('RAJAGAMBAR_MIN_WIDTH', 100),
        'min_height' => env('RAJAGAMBAR_MIN_HEIGHT', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Naming Configuration
    |--------------------------------------------------------------------------
    */
    'naming' => [
        'pattern' => env('RAJAGAMBAR_NAMING_PATTERN', '{timestamp}_{random}_{original}'),
        'timestamp_format' => env('RAJAGAMBAR_TIMESTAMP_FORMAT', 'YmdHis'),
        'random_length' => env('RAJAGAMBAR_RANDOM_LENGTH', 8),
        'allowed_characters' => env('RAJAGAMBAR_ALLOWED_CHARS', 'a-zA-Z0-9._-'),
        'sanitize_filename' => env('RAJAGAMBAR_SANITIZE_FILENAME', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Processing Options
    |--------------------------------------------------------------------------
    */
    'processing' => [
        // Image optimization
        'optimize' => env('RAJAGAMBAR_OPTIMIZE', true),

        // Watermark settings
        'watermark' => [
            'enabled' => env('RAJAGAMBAR_WATERMARK_ENABLED', false),
            'image_path' => env('RAJAGAMBAR_WATERMARK_PATH', null),
            'position' => env('RAJAGAMBAR_WATERMARK_POSITION', 'bottom-right'),
            'opacity' => env('RAJAGAMBAR_WATERMARK_OPACITY', 50),
            'margin' => env('RAJAGAMBAR_WATERMARK_MARGIN', 10),
        ],

        // Color adjustments
        'adjustments' => [
            'brightness' => env('RAJAGAMBAR_BRIGHTNESS', 0), // -100 to 100
            'contrast' => env('RAJAGAMBAR_CONTRAST', 0), // -100 to 100
            'gamma' => env('RAJAGAMBAR_GAMMA', 1.0), // 0.1 to 3.0
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        'scan_uploads' => env('RAJAGAMBAR_SCAN_UPLOADS', true),
        'allowed_mime_types' => [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/bmp',
            'image/tiff',
        ],
        'blocked_extensions' => ['php', 'exe', 'bat', 'cmd', 'com', 'scr'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'memory_limit' => env('RAJAGAMBAR_MEMORY_LIMIT', '256M'),
        'timeout' => env('RAJAGAMBAR_TIMEOUT', 30), // seconds
        'queue_processing' => env('RAJAGAMBAR_QUEUE_PROCESSING', false),
        'cache_processed' => env('RAJAGAMBAR_CACHE_PROCESSED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('RAJAGAMBAR_LOGGING_ENABLED', true),
        'level' => env('RAJAGAMBAR_LOG_LEVEL', 'info'),
        'channel' => env('RAJAGAMBAR_LOG_CHANNEL', 'single'),
    ],
];
