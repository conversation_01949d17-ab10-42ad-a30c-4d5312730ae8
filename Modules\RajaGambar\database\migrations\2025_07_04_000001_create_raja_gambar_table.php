<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('raja_gambar', function (Blueprint $table) {
            $table->id();
            $table->string('file', 500)->index(); // Path to the file
            $table->json('json')->nullable(); // Additional metadata
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('raja_gambar');
    }
};
