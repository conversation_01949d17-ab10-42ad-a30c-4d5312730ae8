<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('raja_gambar', function (Blueprint $table) {
            $table->string('model_type')->nullable()->after('user_id');
            $table->unsignedBigInteger('model_id')->nullable()->after('model_type');
            $table->string('field_name')->nullable()->after('model_id');

            // Add index for better performance
            $table->index(['model_type', 'model_id', 'field_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('raja_gambar', function (Blueprint $table) {
            $table->dropIndex(['model_type', 'model_id', 'field_name']);
            $table->dropColumn(['model_type', 'model_id', 'field_name']);
        });
    }
};
