@php
    $record = $getRecord();
    $imagePath = $record->file ?? '';
    $imageUrl = '';
    
    // Try to construct proper image URL
    if ($imagePath) {
        // Check if it's already a full URL
        if (str_starts_with($imagePath, 'http')) {
            $imageUrl = $imagePath;
        } else {
            // Try different storage paths
            $possiblePaths = [
                '/storage/' . $imagePath,
                '/storage/uploads/' . $imagePath,
                '/' . $imagePath,
            ];
            
            foreach ($possiblePaths as $path) {
                if (file_exists(public_path($path))) {
                    $imageUrl = $path;
                    break;
                }
            }
            
            // Fallback to first path if none found
            if (!$imageUrl) {
                $imageUrl = '/storage/' . $imagePath;
            }
        }
    }
    
    // Get metadata
    $json = is_array($record->json) ? $record->json : json_decode($record->json, true);
    $width = $json['width'] ?? 0;
    $height = $json['height'] ?? 0;
    $fileSize = $json['file_size'] ?? 0;
    $mimeType = $json['mime_type'] ?? '';
    $originalName = $json['original_name'] ?? basename($imagePath);
@endphp

<div class="space-y-6">
    @if($imageUrl)
        <div class="flex justify-center">
            <div class="relative group">
                <img 
                    src="{{ $imageUrl }}" 
                    alt="{{ $originalName }}"
                    class="max-w-full max-h-80 rounded-lg shadow-lg cursor-pointer transition-transform hover:scale-105"
                    onclick="openImageModal('{{ $imageUrl }}', '{{ addslashes($originalName) }}')"
                    onerror="this.src='/noimage.jpg'; this.onerror=null;"
                />
                <div class="absolute inset-0 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                    <div class="text-white opacity-0 group-hover:opacity-100 transition-opacity text-center">
                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                        </svg>
                        <p class="text-sm">Click to enlarge</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-semibold text-gray-800 mb-3">Quick Info</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                @if($width && $height)
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                        </svg>
                        <span><strong>Size:</strong> {{ $width }} × {{ $height }} px</span>
                    </div>
                @endif
                
                @if($fileSize)
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <span><strong>File Size:</strong> {{ number_format($fileSize / 1024, 2) }} KB</span>
                    </div>
                @endif
                
                @if($mimeType)
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span><strong>Type:</strong> {{ strtoupper(str_replace('image/', '', $mimeType)) }}</span>
                    </div>
                @endif
            </div>
        </div>
        
        <div class="bg-blue-50 rounded-lg p-4">
            <h4 class="font-semibold text-blue-800 mb-2">File Access</h4>
            <div class="space-y-2 text-sm">
                <div>
                    <strong class="text-blue-700">Direct URL:</strong> 
                    <a href="{{ $imageUrl }}" target="_blank" class="text-blue-600 hover:underline break-all">{{ $imageUrl }}</a>
                </div>
                <div>
                    <strong class="text-blue-700">Storage Path:</strong> 
                    <code class="bg-blue-100 px-2 py-1 rounded text-xs">{{ $imagePath }}</code>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-12 text-gray-500">
            <svg class="w-20 h-20 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-700 mb-2">No Image Available</h3>
            <p class="text-gray-500">The image file could not be found or loaded.</p>
        </div>
    @endif
</div>

<!-- Modal for full-size image -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden items-center justify-center p-4" onclick="closeImageModal()">
    <div class="relative max-w-6xl max-h-full">
        <img id="modalImage" src="" alt="" class="max-w-full max-h-full rounded-lg" onclick="event.stopPropagation()">
        <button 
            onclick="closeImageModal()" 
            class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all"
        >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        <div class="absolute bottom-4 left-4 right-4 text-center">
            <p class="text-white bg-black bg-opacity-50 rounded px-3 py-1 text-sm" id="modalImageName"></p>
        </div>
    </div>
</div>

<script>
function openImageModal(imageUrl, imageName) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalImageName = document.getElementById('modalImageName');
    modalImage.src = imageUrl;
    modalImage.alt = imageName;
    modalImageName.textContent = imageName;
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    document.body.style.overflow = 'auto';
}

// Close modal on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});
</script>
