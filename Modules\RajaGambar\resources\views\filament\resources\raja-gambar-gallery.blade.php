@php
    use Filament\Support\Facades\FilamentAsset;
    use Filament\Support\Facades\FilamentView;
@endphp

<style>
    .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
        padding: 1rem;
    }
    
    .gallery-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 1rem;
        background: white;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease-in-out;
        overflow: hidden;
    }
    
    .gallery-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    .gallery-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 0.5rem;
        margin-bottom: 0.75rem;
    }
    
    .gallery-info {
        font-size: 0.875rem;
        color: #6b7280;
    }
    
    .gallery-title {
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.5rem;
        word-break: break-all;
    }
    
    .gallery-actions {
        margin-top: 0.75rem;
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }
    
    .gallery-action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
    }
    
    .gallery-action-view {
        background-color: #3b82f6;
        color: white;
    }
    
    .gallery-action-view:hover {
        background-color: #2563eb;
    }
    
    .gallery-action-edit {
        background-color: #f59e0b;
        color: white;
    }
    
    .gallery-action-edit:hover {
        background-color: #d97706;
    }
    
    .gallery-action-delete {
        background-color: #ef4444;
        color: white;
    }
    
    .gallery-action-delete:hover {
        background-color: #dc2626;
    }
    
    @media (max-width: 640px) {
        .gallery-grid {
            grid-template-columns: repeat(1, 1fr);
        }
    }
    
    @media (min-width: 641px) and (max-width: 768px) {
        .gallery-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (min-width: 769px) and (max-width: 1024px) {
        .gallery-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (min-width: 1025px) and (max-width: 1280px) {
        .gallery-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }
    
    @media (min-width: 1281px) {
        .gallery-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }
</style>

<div class="gallery-grid">
    @foreach($records as $record)
        <div class="gallery-card">
            <img 
                src="{{ $record->file ? '/storage/' . $record->file : '/noimage.jpg' }}" 
                alt="{{ $record->original_name ?? 'Image' }}"
                class="gallery-image"
                onerror="this.src='/noimage.jpg'"
            >
            
            <div class="gallery-title">
                {{ Str::limit($record->original_name ?? 'Unnamed', 30) }}
            </div>
            
            <div class="gallery-info">
                <div><strong>Size:</strong> {{ $record->file_size ? number_format($record->file_size / 1024, 1) . ' KB' : 'Unknown' }}</div>
                <div><strong>Type:</strong> {{ $record->mime_type ?? 'Unknown' }}</div>
                <div><strong>Uploaded:</strong> {{ $record->created_at?->format('M d, Y') }}</div>
            </div>
            
            <div class="gallery-actions">
                <a href="{{ route('filament.admin.resources.raja-gambars.view', $record) }}" 
                   class="gallery-action-btn gallery-action-view">
                    View
                </a>
                <a href="{{ route('filament.admin.resources.raja-gambars.edit', $record) }}" 
                   class="gallery-action-btn gallery-action-edit">
                    Edit
                </a>
                <form method="POST" 
                      action="{{ route('filament.admin.resources.raja-gambars.destroy', $record) }}" 
                      style="display: inline;"
                      onsubmit="return confirm('Are you sure you want to delete this image?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="gallery-action-btn gallery-action-delete">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    @endforeach
</div>

@if($records->isEmpty())
    <div style="text-align: center; padding: 3rem; color: #6b7280;">
        <svg style="width: 4rem; height: 4rem; margin: 0 auto 1rem; opacity: 0.5;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 0.5rem;">No images found</h3>
        <p>Upload some images to see them in the gallery.</p>
    </div>
@endif
