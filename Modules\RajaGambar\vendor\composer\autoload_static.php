<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba
{
    public static $files = array (
        '545b95e86adcdc11d1c161e3997378a0' => __DIR__ . '/../..' . '/app/Helpers/RajaGambarHelper.php',
    );

    public static $prefixLengthsPsr4 = array (
        'M' => 
        array (
            'Modules\\RajaGambar\\Tests\\' => 25,
            'Modules\\RajaGambar\\Database\\Seeders\\' => 36,
            'Modules\\RajaGambar\\Database\\Factories\\' => 38,
            'Modules\\RajaGambar\\' => 19,
        ),
        'I' => 
        array (
            'Intervention\\Image\\' => 19,
            'Intervention\\Gif\\' => 17,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Modules\\RajaGambar\\Tests\\' => 
        array (
            0 => __DIR__ . '/../..' . '/tests',
        ),
        'Modules\\RajaGambar\\Database\\Seeders\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/seeders',
        ),
        'Modules\\RajaGambar\\Database\\Factories\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/factories',
        ),
        'Modules\\RajaGambar\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
        'Intervention\\Image\\' => 
        array (
            0 => __DIR__ . '/..' . '/intervention/image/src',
        ),
        'Intervention\\Gif\\' => 
        array (
            0 => __DIR__ . '/..' . '/intervention/gif/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba::$classMap;

        }, null, ClassLoader::class);
    }
}
