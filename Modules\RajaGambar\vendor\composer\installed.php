<?php return array(
    'root' => array(
        'name' => 'nwidart/rajagambar',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => 'c1fc0e03b7b804264979fd9d0d508d560efd45ea',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'intervention/gif' => array(
            'pretty_version' => '4.2.2',
            'version' => '4.2.2.0',
            'reference' => '5999eac6a39aa760fb803bc809e8909ee67b451a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/gif',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image' => array(
            'pretty_version' => '3.11.3',
            'version' => '3.11.3.0',
            'reference' => 'd0f097b8a3fa8fb758efc9440b513aa3833cda17',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nwidart/rajagambar' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'c1fc0e03b7b804264979fd9d0d508d560efd45ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
