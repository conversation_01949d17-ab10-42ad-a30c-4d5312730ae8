/* Gallery Grid Layout for <PERSON><PERSON><PERSON><PERSON> */
.gallery-grid-layout .fi-ta-content {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
    padding: 1rem !important;
}

.gallery-grid-layout .fi-ta-row {
    display: block !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    background: white !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease-in-out !important;
}

.gallery-grid-layout .fi-ta-row:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
}

.gallery-grid-layout .fi-ta-cell {
    display: block !important;
    border: none !important;
    padding: 0 !important;
}

.gallery-grid-layout .fi-ta-cell-content {
    text-align: center !important;
}

/* Responsive Grid */
@media (max-width: 640px) {
    .gallery-grid-layout .fi-ta-content {
        grid-template-columns: repeat(1, 1fr) !important;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    .gallery-grid-layout .fi-ta-content {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .gallery-grid-layout .fi-ta-content {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

@media (min-width: 1025px) and (max-width: 1280px) {
    .gallery-grid-layout .fi-ta-content {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

@media (min-width: 1281px) {
    .gallery-grid-layout .fi-ta-content {
        grid-template-columns: repeat(5, 1fr) !important;
    }
}

/* Hide table headers for gallery view */
.gallery-grid-layout .fi-ta-header {
    display: none !important;
}

/* Style the image cards */
.gallery-grid-layout .fi-ta-row img {
    border-radius: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}

.gallery-grid-layout .fi-ta-row .fi-ta-text {
    font-size: 0.875rem !important;
    margin-bottom: 0.25rem !important;
}

.gallery-grid-layout .fi-ta-row .fi-badge {
    margin: 0.25rem auto !important;
    display: inline-block !important;
}
